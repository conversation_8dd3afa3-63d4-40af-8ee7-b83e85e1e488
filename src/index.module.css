.container {
  border-radius: 12px;
  background-color: var(--bg-light-color, #f5f5f5) !important;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  width: 100%;
  min-width: 400px;
  max-width: 528px;
  height: 192px;
  cursor: pointer;
}

.content {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  gap: 12px;
  box-sizing: border-box;
  padding: 0 16px 16px;
  width: 100%;
  height: 144px;
  justify-content: space-between;
}
