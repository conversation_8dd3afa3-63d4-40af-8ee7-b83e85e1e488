.memberColumn {
    display: flex;
    flex-direction: row;
    justify-content: space-around;
    align-items: center;
    flex-wrap: nowrap;
    box-sizing: border-box;
    width: 100%;
    height: 56px;
}

.memberColumn a {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    box-sizing: border-box;
    padding: 0px 10px;
    justify-content: center;
    align-items: center;
    width: 92px;
    height: 44px;
    color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
    text-decoration: none;
}

.memberColumn a:hover {
    cursor: pointer;
    color: var(--tbpc-client-hover-color, #ff5000);
}

.memberColumn a:active {
    cursor: pointer;
    color: var(--tbpc-client-active-color, #FFAD87);
}

.statNumber {
    font-size: 24px;
    line-height: 24px;
    height: 24px;
    font-weight: 600;
    letter-spacing: -0.02em;
}

.statLabel {
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    width: 36px;
    margin-top: 4px;
}