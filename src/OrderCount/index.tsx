import React from 'react';
import { AppearWeb } from '@ali/appear';
import type { IOrderCountRes } from '../types';
import styles from './index.module.css';

interface IOrderCount {
    orderCount: IOrderCountRes;
    goldLogMyTaoClk?: (spmC: string, spmD: string) => void;
    goldLogMyTaoExp?: (spmC: string, spmD: string) => void;
}

function OrderCount(props: IOrderCount) {
    const { orderCount, goldLogMyTaoClk } = props;
    return (
        <AppearWeb>
            <div className={styles.memberColumn} data-spm="profile">
                <a
                    data-spm="dnonpayment"
                    target="_blank"
                    href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitPay"
                    onClick={(e) => {
                        e.stopPropagation();
                        goldLogMyTaoClk?.('profile', 'nonpayment');
                    }}
                >
                    <div className={styles.statNumber}>{orderCount.waitPay}</div>
                    <div className={styles.statLabel}>待付款</div>
                </a>
                <a
                    data-spm="ddelivery"
                    target="_blank"
                    href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitSend"
                    onClick={(e) => {
                        e.stopPropagation();
                        goldLogMyTaoClk?.('profile', 'delivery');
                    }}
                >
                    <div className={styles.statNumber}>{orderCount.waitSend}</div>
                    <div className={styles.statLabel}>待发货</div>
                </a>
                <a
                    data-spm="dawaiting"
                    target="_blank"
                    href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitConfirm"
                    onClick={(e) => {
                        e.stopPropagation();
                        goldLogMyTaoClk?.('profile', 'awaiting');
                    }}
                >
                    <div className={styles.statNumber}>{orderCount?.waitConfirm}</div>
                    <div className={styles.statLabel}>待收货</div>
                </a>
                <a
                    data-spm="dcomment"
                    target="_blank"
                    href="//buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?action=itemlist/BoughtQueryAction&event_submit_do_query=1&tabCode=waitRate"
                    onClick={(e) => {
                        e.stopPropagation();
                        goldLogMyTaoClk?.('profile', 'comment');
                    }}
                >
                    <div className={styles.statNumber}>{orderCount.waitRate}</div>
                    <div className={styles.statLabel}>待评价</div>
                </a>
            </div>
        </AppearWeb>
    );
}

export default OrderCount;
