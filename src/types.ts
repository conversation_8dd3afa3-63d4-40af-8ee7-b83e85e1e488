export interface IOrderCountRes {
  cart: number;
  waitPay: number;
  waitSend: number;
  waitReceive: number;
  waitConfirm: number;
  waitRate: number;
}

export interface OrderLogisticsProps {
  statusItems?: StatusItemData[];
  products?: ProductItemData[];
  className?: string;
  queryOrderListCount?: () => Promise<IQueryOrderListCountRes>;
  queryOrderStatus?: () => Promise<IQueryOrderStatusRes>;
  goldLogMyTaoClk?: (spmC: string, spmD: string) => void;
  goldLogMyTaoExp?: (spmC: string, spmD: string) => void;
  homeUrl?: string;
}

export interface IOrderStatusItem {
  tabCode: TAB_CODE_KEY;
  count: number;
}

export interface IQueryOrderListCountRes {
  result: IOrderStatusItem[];
}

export enum TAB_CODE_KEY {
  'waitConfirm' = 'waitConfirm',
  'waitPay' = 'waitPay',
  'waitRate' = 'waitRate',
  'waitSend' = 'waitSend',
}

export interface ISubSection {
  waitPaymentInfo?: {
    [key: string]: any;
  };
  logisticsDeliveryInfo?: {
    [key: string]: any;
  };
}


export interface IQueryOrderStatusRes {
  containers: {
    entrance_home_main_pc: {
      base: {
        sections: {
          sectionBizCode: string;
          subSection: ISubSection;
        }[];
      };
    };
  };
}

export interface ProductItemData {
  id?: string;
  image: string;
  statusText: string;
  type?: string;
  category?: string;
  source?: string;
  targetUrl?: string;
  clickParam?: any;
  subTitle?: string;
  deliveryStatus?: string;
  deliverySubTitle?: string;
  paymentAmount?: any;
  paymentDeadline?: any;
  smartContent?: {
    itemImgUrl?: string;
    statusName?: string;
    subTitle?: string;
    amount?: any;
    deadline?: any;
  };
  content?: {
    itemImgUrl?: string;
    title?: string;
    amount?: any;
    deadline?: any;
  };
}

export interface StatusItemData {
  icon: string;
  text: string;
  count: string;
}

export interface LogisticsData {
  isFetching: boolean;
  data: ProductItemData[];
}
