import React from 'react';
    import styles from './index.module.css';

    interface StatusItemProps {
      icon: string;
      text: string;
      count: string;
    }

    const StatusItem: React.FC<StatusItemProps> = ({ icon, text, count }) => {
      return (
        <div className={styles.statusItem}>
          <img src={icon} alt={text} className={styles.icon} />
          <div className={styles.textContainer}>
            <span className={styles.text}>{text}</span>
            <span className={styles.count}>{count}</span>
          </div>
        </div>
      );
    };

    export default StatusItem;
