import React from 'react';
import styles from './index.module.css';
import StatusItem from './StatusItem';
import { StatusItemData } from '../types';

interface LogisticsStatusProps {
  statusItems: StatusItemData[];
}

const LogisticsStatus: React.FC<LogisticsStatusProps> = ({ statusItems }) => {
  return (
    <div className={styles.statusContainer}>
      {statusItems.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && <div className={styles.divider}></div>}
          <StatusItem icon={item.icon} text={item.text} count={item.count} />
        </React.Fragment>
      ))}
    </div>
  );
};

export default LogisticsStatus;
