import React from 'react';
import styles from './index.module.css';

interface EmptyStateProps {
  onAllOrdersClick?: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({ onAllOrdersClick }) => {
  return (
    <div className={styles.emptyState}>
      <div className={styles.emptyText}>暂无订单物流信息~</div>
      <div className={styles.allOrdersLink} onClick={onAllOrdersClick}>
        全部订单 &gt;
      </div>
    </div>
  );
};

export default EmptyState;