import React, { useState, useEffect, useRef } from 'react';
import styles from './index.module.css';
import ProductItem from './ProductItem';
import ProductPlaceholder from './ProductPlaceholder';
import EmptyState from './EmptyState';
import { ProductItemData } from '../types';

interface ProductListProps {
  products: ProductItemData[];
  onAllOrdersClick?: () => void;
  isFetching?: boolean;
  goldLogMyTaoClk?: (spmC: string, spmD: string) => void;
}

const ProductList: React.FC<ProductListProps> = ({
  products,
  onAllOrdersClick,
  isFetching = false,
  goldLogMyTaoClk,
}) => {
  const [containerWidth, setContainerWidth] = useState(496); // 默认宽度
  const [scrollPosition, setScrollPosition] = useState(0);
  const [showLeftButton, setShowLeftButton] = useState(false);
  const [showRightButton, setShowRightButton] = useState(false);
  const [itemList, setItemList] = useState([]);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    let keys: any = {
      waitSend: {
        color: '#FFBB33',
        title: '待发货',
        list: [] as any,
        tabCode: 'waitSend',
      },
      transport: {
        color: '#FF6A26',
        title: '运输中',
        list: [] as any,
        tabCode: 'waitConfirm',
      },
      delivering: {
        color: '#FF6A26',
        title: '派送中',
        list: [] as any,
        tabCode: 'waitConfirm',
      },
      sign: {
        color: '#18CC54',
        title: '已签收',
        list: [] as any,
        tabCode: 'waitConfirm',
      },
    };

    products.forEach((item: any) => {
      if (keys[item.tabName] && item.expressList && item.expressList.length) {
        item.expressList.forEach((express: any) => {
          keys[item.tabName].list.push({
            ...express?.subList[0],
            color: keys[item.tabName].color,
            title: keys[item.tabName].title,
            targetUrl: `https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?tabCode=${
              keys[item.tabName].tabCode
            }`,
          });
        });
      }
    });
    const arr1 = keys.waitSend.list;
    const arr2 = keys.transport.list;
    const arr3 = keys.delivering.list;
    const arr4 = keys.sign.list;
    setItemList(arr1.concat(arr2).concat(arr3).concat(arr4));
  }, [products]);

  // 监听容器宽度变化和滚动状态
  useEffect(() => {
    const updateContainerWidth = () => {
      if (containerRef.current) {
        const width = containerRef.current.offsetWidth;
        setContainerWidth(width);
        updateScrollButtons();
      }
    };

    const updateScrollButtons = () => {
      if (containerRef.current) {
        const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
        setShowLeftButton(scrollLeft > 0);
        setShowRightButton(scrollLeft < scrollWidth - clientWidth - 1);
        setScrollPosition(scrollLeft);
      }
    };

    updateContainerWidth();
    window.addEventListener('resize', updateContainerWidth);

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', updateScrollButtons);
      // 初始检查是否需要显示滚动按钮
      setTimeout(updateScrollButtons, 100);
    }

    return () => {
      window.removeEventListener('resize', updateContainerWidth);
      if (container) {
        container.removeEventListener('scroll', updateScrollButtons);
      }
    };
  }, [products]);

  const scrollLeft = () => {
    if (containerRef.current) {
      const scrollAmount = 200;
      containerRef.current.scrollBy({
        left: -scrollAmount,
        behavior: 'smooth',
      });
    }
  };

  const scrollRight = () => {
    if (containerRef.current) {
      const scrollAmount = 200;
      containerRef.current.scrollBy({ left: scrollAmount, behavior: 'smooth' });
    }
  };

  // 加载状态显示占位符
  if (isFetching) {
    return null;
    // const maxItemsPerRow = 7;
    // return (
    //   <div className={styles.productListWrapper}>
    //     <div className={styles.productList}>
    //       {Array.from({ length: maxItemsPerRow }, (_, index) => (
    //         <ProductPlaceholder key={`loading-placeholder-${index}`} />
    //       ))}
    //     </div>
    //   </div>
    // );
  }

  // 空状态
  if (products.length === 0) {
    return <EmptyState onAllOrdersClick={onAllOrdersClick} />;
  }

  // 简化逻辑，直接显示所有商品，自适应布局
  // 动态计算占位符数量，确保显示完整
  const maxItems = 7;
  const minItemWidth = 56; // 最小宽度56px
  const gap = 12; // gap间距

  // 计算容器可以容纳的最大项目数，使用动态容器宽度
  const maxVisibleItems = Math.floor(
    (containerWidth + gap) / (minItemWidth + gap),
  );

  // 占位符数量：不超过容器能显示的数量，且不超过原定的最大数量
  const idealPlaceholderCount = Math.max(0, maxItems - itemList.length);
  const maxAllowedPlaceholders = Math.max(0, maxVisibleItems - itemList.length);
  const placeholderCount = Math.min(
    idealPlaceholderCount,
    maxAllowedPlaceholders,
  );

  return (
    <div className={styles.productListWrapper}>
      {showLeftButton && placeholderCount === 0 && (
        <button
          className={`${styles.scrollButton} ${styles.scrollButtonLeft}`}
          onClick={(e) => {
            e.stopPropagation();
            scrollLeft();
          }}
        >
          <img
            src="https://img.alicdn.com/imgextra/i4/6000000003955/O1CN01GqTaav1f5RWG1XCSq_!!6000000003955-2-gg_dtc.png"
            alt="向左滚动"
            className={styles.scrollIcon}
          />
        </button>
      )}
      <div className={styles.productList} ref={containerRef}>
        {itemList.map((item: any, index: number) => {
          return (
            <ProductItem
              key={index}
              image={item.pic || ''}
              statusText={item.title}
              targetUrl={item.targetUrl || ''}
              color={item.color}
              index={index}
              goldLogMyTaoClk={goldLogMyTaoClk}
            />
          );
        })}
        {/* {products.map((product, index) => (
          <ProductItem
            key={index}
            image={product.image}
            statusText={product.statusText}
            type={product.type}
            targetUrl={product.targetUrl || ''}
            index={index}
            goldLogMyTaoClk={goldLogMyTaoClk}
          />
        ))} */}
        {placeholderCount > 0 &&
          Array.from({ length: placeholderCount }, (_, index) => (
            <ProductPlaceholder key={`placeholder-${index}`} />
          ))}
      </div>
      {showRightButton && placeholderCount === 0 && (
        <button
          className={`${styles.scrollButton} ${styles.scrollButtonRight}`}
          onClick={(e) => {
            e.stopPropagation();
            scrollRight();
          }}
        >
          <img
            src="https://img.alicdn.com/imgextra/i4/6000000003955/O1CN01GqTaav1f5RWG1XCSq_!!6000000003955-2-gg_dtc.png"
            alt="向右滚动"
            className={styles.scrollIcon}
          />
        </button>
      )}
    </div>
  );
};

export default ProductList;
