.productListWrapper {
  position: relative;
  width: 100%;
  height: 60px;
}

.productList {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 12px;
  width: 100%;
  height: 60px;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.productList::-webkit-scrollbar {
  display: none;
}

.scrollButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  cursor: pointer;
  transition: all 0.2s;
  z-index: 2;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.scrollButton:hover {
  background: rgba(255, 255, 255, 1);
  color: #333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.scrollButton:active {
  transform: translateY(-50%) scale(0.95);
}

.scrollButtonLeft {
  left: -24px;
}

.scrollButtonRight {
  right: -24px;
}

.scrollIcon {
  width: 32px;
  height: 32px;
}

.scrollButtonRight .scrollIcon {
  transform: scaleX(-1);
}
