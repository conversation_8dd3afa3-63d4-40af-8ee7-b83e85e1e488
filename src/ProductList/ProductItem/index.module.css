.productItem {
  position: relative;
  min-width: 56px;
  height: 60px;
  flex: 1 1 56px;
  border-radius: 8px;
  overflow: hidden;
}

.imageContainer {
  position: relative;
  width: 100%;
  height: 100%;
}

.imageContainer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.04);
  pointer-events: none;
  z-index: 1;
}

.productImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.statusText {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ff6a26;
  color: white;
  font-size: 10px;
  text-align: center;
  padding: 2px 4px;
  border-radius: 0 0 8px 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
