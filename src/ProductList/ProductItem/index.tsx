import React from 'react';
import styles from './index.module.css';

interface ProductItemProps {
  targetUrl: string;
  image: string;
  statusText: string;
  color: string;
  index?: number;
  goldLogMyTaoClk?: (spmC: string, spmD: string) => void;
}

const ProductItem: React.FC<ProductItemProps> = ({
  targetUrl,
  image,
  statusText,
  color,
  index,
  goldLogMyTaoClk,
}) => {
  return (
    <a
      className={styles.productItem}
      href={targetUrl}
      target="_blank"
      onClick={(e) => {
        e.stopPropagation();
        // 订单list整体点击额外区分商品坑位
        const position = index !== undefined ? (index + 1).toString() : '1';
        goldLogMyTaoClk?.('profile', `product_${position}`);
      }}
    >
      <div className={styles.imageContainer}>
        <img src={image} alt={statusText} className={styles.productImage} />
        <div className={styles.statusText} style={{ backgroundColor: color }}>
          {statusText}
        </div>
      </div>
    </a>
  );
};

export default ProductItem;
