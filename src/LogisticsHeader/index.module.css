.header {
  border-radius: 12px;
  box-sizing: border-box;
  padding: 12px 16px;
  width: 100%;
  height: 48px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.titleContainer {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  gap: 4px;
  align-items: center;
  width: 64px;
  height: 24px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  color: var(--tbpc-primary-color, rgba(0, 0, 0, 0.92));
}
