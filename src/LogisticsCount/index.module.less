.wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: calc(100% / 4 - (3 * 36px) / 4);
  text-decoration: none;
  img {
    width: 32px;
    height: 32px;
  }
  p {
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: normal;
    line-height: 16px;
    text-align: center;
    color: rgba(0, 0, 0, 0.92);
    margin: 0;
  }
}

.line {
  width: 36px;
  height: 1px;
  display: flex;
  flex-direction: column;
  border-radius: 1000px;
  background: rgba(0, 0, 0, 0.6);
}
