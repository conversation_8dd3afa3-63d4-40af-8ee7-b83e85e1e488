import React, { Fragment, useEffect, useState } from 'react';
import styles from './index.module.less';

const LogisticsCount = (props: any) => {
  const { products = [] } = props;
  const itemMap = [
    {
      title: '待发货',
      key: 'waitSend',
      img: 'https://gw.alicdn.com/imgextra/i4/O1CN01SkUgwq1hVfQgUx7YT_!!6000000004283-2-tps-64-64.png',
      targetUrl:
        'https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?tabCode=waitSend',
    },
    {
      title: '运输中',
      key: 'transport',
      img: 'https://gw.alicdn.com/imgextra/i4/O1CN01OJT5Xz23DH4LiXJ2A_!!6000000007221-2-tps-64-64.png',
      targetUrl:
        'https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?tabCode=waitConfirm',
    },
    {
      title: '派送中',
      key: 'delivering',
      img: 'https://gw.alicdn.com/imgextra/i2/O1CN01mf6Tbi1e2rfstAYxE_!!6000000003814-2-tps-64-64.png',
      targetUrl:
        'https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?tabCode=waitConfirm',
    },
    {
      title: '已签收',
      key: 'sign',
      img: 'https://gw.alicdn.com/imgextra/i1/O1CN012RMu0K1XcP66mlUWJ_!!6000000002944-2-tps-64-64.png',
      targetUrl:
        'https://buyertrade.taobao.com/trade/itemlist/list_bought_items.htm?tabCode=waitConfirm',
    },
  ];

  const [itemCount, setItemCount] = useState({} as any);

  useEffect(() => {
    let keys: any = {
      waitSend: { title: '待发货', count: 0 },
      transport: { title: '运输中', count: 0 },
      delivering: { title: '派送中', count: 0 },
      sign: { title: '已签收', count: 0 },
    };
    products.forEach((item: any) => {
      if (keys[item.tabName]) {
        keys[item.tabName].count = item.count;
      }
    });
    setItemCount(keys);
  }, [products]);

  return (
    <div className={styles.wrap}>
      {itemMap.map((item, index) => {
        return (
          <Fragment key={`${item.title}${index}`}>
            <a
              href={item.targetUrl}
              target="_blank"
              className={styles.item}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              <img src={item.img} alt={item.title} />
              <p>
                {item.title} {itemCount[item.key]?.count || 0}
              </p>
            </a>
            {index < 3 ? <div className={styles.line} key={index} /> : ''}
          </Fragment>
        );
      })}
    </div>
  );
};

export default LogisticsCount;
