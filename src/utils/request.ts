const mtopRequest = (config: any) => {
  if (!window?.lib?.mtop?.request) {
    return Promise.reject(new Error('Mtop不存在'));
  }
  const Mtop = window.lib.mtop;

  return Mtop.request({
    v: '1.0',
    ecode: config.needLogin ? 1 : 0,
    timeout: 5000,
    dataType: 'jsonp',
    valueType: 'original',
    jsonpIncPrefix: 'tbpcElectronClient',
    // ttid: ttid,
    ...config,
    data: {
      ...config.data,
    },
  })
    .then((response: any) => {
      if (response?.data) {
        return Object.assign({}, response.data, { traceId: response.traceId });
      }
      return response;
    })
    .catch((errorResponse: any) => {
      // eslint-disable-next-line no-negated-condition
      if (config.needRetry !== false) {
        return mtopRequest({ ...config, needRetry: false });
      } else {
        throw errorResponse;
      }
    });
};

// 物流信息请求--follow移动端接口
export const requestLogistics = () => {
  return mtopRequest({
    api: 'mtop.taobao.amp.logistics.page',
    v: '1.0',
    data: { queryTab: 'all', queryType: 'all' },
    ecode: 0,
    type: 'GET',
    dataType: 'jsonp',
    timeout: 5000,
  }) as Promise<any>;
};
