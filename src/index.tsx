import React, { useEffect, useState, useMemo } from 'react';
import styles from './index.module.css';
import LogisticsHeader from './LogisticsHeader';
// import OrderCount from './OrderCount';
import LogisticsCount from './LogisticsCount';
import ProductList from './ProductList';
import {
  OrderLogisticsProps,
  ProductItemData,
  // LogisticsData,
  // IQueryOrderListCountRes,
  // IQueryOrderStatusRes,
} from './types';
import { AppearWeb } from '@ali/appear';
import { requestLogistics } from './utils/request';

const defaultProducts: ProductItemData[] = [];

const DEFAULT_ORDER_COUNT = {
  cart: 0,
  waitPay: 0,
  waitSend: 0,
  waitReceive: 0,
  waitConfirm: 0,
  waitRate: 0,
};

const OrderLogistics: React.FC<OrderLogisticsProps> = ({
  products = defaultProducts,
  className,
  // queryOrderListCount,
  // queryOrderStatus,
  goldLogMyTaoClk,
  goldLogMyTaoExp,
  homeUrl,
}) => {
  // const [orderCount, setOrderCount] = useState(DEFAULT_ORDER_COUNT);
  // const [logisticsData, setLogisticsData] = useState<LogisticsData>({
  //   isFetching: true,
  //   data: [],
  // });

  // 物流信息
  const [isLoading, setLoading] = useState(true);
  const [transformedProducts, setTransformedProducts] = useState([]);

  // 获取订单数量数据
  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       // 分别处理两个数据获取
  //       if (queryOrderListCount) {
  //         try {
  //           const orderCountRes = await queryOrderListCount();
  //           orderCountRender(orderCountRes, 0);
  //         } catch (error) {
  //           console.error('获取订单数量失败:', error);
  //         }
  //       }

  //       if (queryOrderStatus) {
  //         try {
  //           const orderStatusRes = await queryOrderStatus();
  //           processLogisticsData(orderStatusRes);
  //         } catch (error) {
  //           console.error('获取物流数据失败:', error);
  //         }
  //       }
  //     } catch (error) {
  //       console.error('获取数据失败:', error);
  //       setOrderCount(DEFAULT_ORDER_COUNT);
  //       setLogisticsData({ isFetching: false, data: [] });
  //     }
  //   };

  //   fetchData();
  // }, [queryOrderListCount, queryOrderStatus]);

  // 处理订单数量数据的函数
  // const orderCountRender = (
  //   orderCountRes: IQueryOrderListCountRes,
  //   cartCount: number,
  // ) => {
  //   const resOrderCount = { ...DEFAULT_ORDER_COUNT };
  //   if (Array.isArray(orderCountRes?.result)) {
  //     orderCountRes?.result?.forEach((item) => {
  //       if (item.tabCode) {
  //         resOrderCount[item.tabCode as keyof typeof resOrderCount] =
  //           item.count || 0;
  //       }
  //     });
  //   }
  //   resOrderCount.cart = cartCount;
  //   setOrderCount(resOrderCount);
  // };

  // 处理物流和付款数据
  // const processLogisticsData = (orderStatusRes: IQueryOrderStatusRes) => {
  //   try {
  //     const sections =
  //       orderStatusRes?.containers?.entrance_home_main_pc?.base?.sections;
  //     const subSection =
  //       sections?.find((s) => s.sectionBizCode === 'pcUserInfo')?.subSection ||
  //       {};

  //     let allItems: ProductItemData[] = [];

  //     // 处理待付款信息
  //     if (subSection.waitPaymentInfo?.item) {
  //       const paymentItems = Object.values(subSection.waitPaymentInfo.item).map(
  //         (item) => ({
  //           ...(item as any),
  //           type: 'payment',
  //           category: 'payment',
  //           source: 'waitPaymentInfo',
  //         }),
  //       );
  //       allItems = allItems.concat(paymentItems);
  //     }

  //     // 处理物流配送信息
  //     if (subSection.logisticsDeliveryInfo?.item) {
  //       const deliveryItems = Object.values(
  //         subSection.logisticsDeliveryInfo.item,
  //       ).map((item) => ({
  //         ...(item as any),
  //         type: 'logistics',
  //         category: 'delivery',
  //         source: 'logisticsDeliveryInfo',
  //       }));
  //       allItems = allItems.concat(deliveryItems);
  //     }

  //     setLogisticsData({
  //       isFetching: false,
  //       data: allItems,
  //     });
  //   } catch (error) {
  //     console.error('处理数据失败:', error);
  //     setLogisticsData({ isFetching: false, data: [] });
  //   }
  // };

  useEffect(() => {
    getLogisticsData();
  }, []);

  // 请求物流信息数据
  const getLogisticsData = async () => {
    setLoading(true);
    const res = await requestLogistics();
    const { mainPages = [] } = res;
    setLoading(false);
    if (mainPages.length) {
      mainPages.forEach((i) => {
        if (i.pageName === 'receivePage') {
          setTransformedProducts(i.subTabs || []);
        }
      });
    }
  };

  // 转换数据为 ProductList 所需格式
  // const transformedProducts = useMemo(() => {
  //   if (logisticsData.isFetching || logisticsData.data.length === 0) {
  //     return products;
  //   }

  //   return logisticsData.data.map((item, index) => {
  //     const baseData = {
  //       id: `${item.category}-${index}`,
  //       type: item.type,
  //       category: item.category,
  //       source: item.source,
  //       targetUrl: item.targetUrl || '',
  //       clickParam: item.clickParam
  //     };

  //     if (item.category === 'delivery') {
  //       return {
  //         ...baseData,
  //         image: item.smartContent?.itemImgUrl || '',
  //         statusText: item.smartContent?.statusName || '未知状态',
  //         subTitle: item.smartContent?.subTitle || '',
  //         deliveryStatus: item.smartContent?.statusName,
  //         deliverySubTitle: item.smartContent?.subTitle
  //       };
  //     }

  //     if (item.category === 'payment') {
  //       return {
  //         ...baseData,
  //         image: item.smartContent?.itemImgUrl || item.content?.itemImgUrl || '',
  //         statusText: '待付款',
  //         subTitle: item.smartContent?.subTitle || item.content?.title || '等待付款',
  //         paymentAmount: item.smartContent?.amount || item.content?.amount,
  //         paymentDeadline: item.smartContent?.deadline || item.content?.deadline
  //       };
  //     }

  //     return {
  //       ...baseData,
  //       image: item.smartContent?.itemImgUrl || '',
  //       statusText: item.smartContent?.statusName || '未知状态',
  //       subTitle: item.smartContent?.subTitle || ''
  //     };
  //   });
  // }, [logisticsData, products]);

  const containerClass = className
    ? `${styles.container} ${className}`
    : styles.container;

  return (
    <AppearWeb
      onFirstAppear={() => {
        // 新订单模块曝光
        goldLogMyTaoExp?.('profile', 'mytao');
      }}
    >
      <div
        className={containerClass}
        data-spm="mytao"
        onClick={() => {
          // 新订单模块整体点击
          goldLogMyTaoClk?.('profile', 'mytao');
          if (homeUrl) {
            window.open(homeUrl);
          }
        }}
      >
        <LogisticsHeader />
        <div className={styles.content}>
          {/* <OrderCount
            orderCount={orderCount}
            goldLogMyTaoClk={goldLogMyTaoClk}
            goldLogMyTaoExp={goldLogMyTaoExp}
          /> */}
          <LogisticsCount products={transformedProducts} />

          <ProductList
            products={transformedProducts}
            isFetching={isLoading}
            // isFetching={logisticsData.isFetching}
            goldLogMyTaoClk={goldLogMyTaoClk}
          />
        </div>
      </div>
    </AppearWeb>
  );
};

export default OrderLogistics;
