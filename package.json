{"name": "@ali/tbhome-client-order-logistics", "version": "1.0.2", "description": "", "files": ["esm", "es2017", "cjs", "dist"], "main": "esm/index.js", "module": "esm/index.js", "types": "esm/index.d.ts", "exports": {".": {"es2017": {"types": "./es2017/index.d.ts", "default": "./es2017/index.js"}, "default": {"types": "./esm/index.d.ts", "default": "./esm/index.js"}}, "./*": "./*"}, "sideEffects": ["dist/*", "*.scss", "*.less", "*.css"], "scripts": {"start": "ice-pkg start", "build": "ice-pkg build", "prepublishOnly": "npm run build", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "lint": "npm run eslint && npm run stylelint"}, "keywords": ["ice", "react", "component"], "dependencies": {"@ali/appear": "^1.3.0", "@ice/jsx-runtime": "^0.3.0", "@swc/helpers": "^0.5.1"}, "devDependencies": {"@ali/pkg-plugin-dev": "^1.0.0", "@ali/pkg-plugin-dev-client": "^1.0.0", "@applint/spec": "^1.2.3", "@ice/pkg": "^1.0.0", "@ice/runtime": "^1.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "eslint": "^8.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "stylelint": "^15.0.0"}, "peerDependencies": {"react": "^17 || ^18"}, "publishConfig": {"registry": "https://registry.anpm.alibaba-inc.com"}, "license": "MIT"}