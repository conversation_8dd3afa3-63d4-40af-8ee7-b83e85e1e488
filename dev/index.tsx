import React from 'react';
import Mod from '../esm';

const mtopRequest = (config) => {
  if (!window?.lib?.mtop?.request) {
    return Promise.reject(new Error('Mtop不存在'));
  }
  const Mtop = window.lib.mtop;

  return Mtop.request({
    v: '1.0',
    ecode: config.needLogin ? 1 : 0,
    timeout: 5000,
    dataType: 'jsonp',
    valueType: 'original',
    jsonpIncPrefix: 'tbpcElectronClient',
    // ttid: ttid,
    ...config,
    data: {
      ...config.data,
    },
  })
    .then((response: any) => {
      if (response?.data) {
        return Object.assign({}, response.data, { traceId: response.traceId });
      }
      return response;
    })
    .catch((errorResponse: any) => {
      if (config.needRetry !== false) {
        return mtopRequest({ ...config, needRetry: false });
      } else {
        throw errorResponse;
      }
    });
};

export default () => {
  const queryOrderListCount = () => {
    return mtopRequest({
      api: 'mtop.order.taobao.countv2',
      v: '1.0',
      data: {
        tabCodes: 'waitConfirm,waitSend,waitPay,waitRate',
      },
      ecode: 0,
      type: 'GET',
      dataType: 'jsonp',
      timeout: 5000,
    }) as Promise<any>;
  };

  const queryOrderStatus = () => {
    return mtopRequest({
      // https://yuque.antfin-inc.com/docs/share/4fbffdd7-44f3-4bf0-bfba-5001335bde22?#
      api: 'mtop.taobao.wireless.home.awesome.pc.get',
      v: '1.0',
      data: {
        containerParams: JSON.stringify({
          entrance_home_main_pc: {
            deltaSections: [],
            passParams: { lastVersion: 'v1' },
            bizParams: {},
            baseCacheTime: '0',
            deltaCacheTime: '0',
            requestType: 'pullRefresh',
          },
        }),
        type: 'GET',
        timeout: 10000,
      },
    }) as Promise<any>;
  };

  return <Mod />;
};
